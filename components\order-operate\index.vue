<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <view class="flex justify-end" v-if="orderInfo">
<!--    <navigator class="cu-btn round line-green margin-right" open-type="navigate"
               :url="'/pages/customer-service/customer-service-list/index?shopId='+orderInfo.shopId+ '&orderInfoId='+ orderInfo.id">
      <view class="cuIcon-servicefill">客服</view>
    </navigator>-->
    <!-- 客服按钮 - 白底黑边框黑字 -->
    <button class="cu-btn round line-black margin-right"  @click="openCustomerService">客服
    </button>

    <!--    删除订单-->
    <!--		<button class="cu-btn round line-grey margin-right" @tap="orderDel" :loading="loading" :disabled="loading" type v-if="orderInfo.status == '5' && orderInfo.isPay == '0'">删除订单</button>-->
    <!--    取消订单-->
    <!--		<button class="cu-btn round line-grey margin-right" @tap="orderCancel" :loading="loading" :disabled="loading" type
         v-if="orderInfo.isPay === '0' && !orderInfo.status">取消订单</button>-->
    <!--    查看物流-->
    <!--		<button class="cu-btn round line-grey margin-right" @tap="orderLogistics" :loading="loading" :disabled="loading" type
         v-if="orderInfo.deliveryWay == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">查看物流</button>-->
    <!-- 付款按钮 - 白底黑边框黑字 -->
    <button class="cu-btn round line-black margin-right" @tap="unifiedOrder" :loading="loading" :disabled="loading" type
            v-if="orderInfo.isPay === '0' && orderInfo.status==='10'">付款
    </button>
    <!-- 邀请好友按钮 - 白底黑边框黑字 -->
    <button class="cu-btn round line-black margin-right" @tap="handleInviteFriends"
            :loading="inviteLoading" :disabled="inviteLoading"
            v-if="orderInfo.isPay === '1' && orderInfo.status==='10' ">邀请好友
    </button>
    <!-- 去约档按钮 - 白底黑边框黑字 -->
    <button class="cu-btn round line-black margin-right" @tap="goToAppoint"
            :loading="appointLoading" :disabled="appointLoading"
            v-if="orderInfo.isPay === '1' && orderInfo.status==='11' && orderInfo.statusDesc==='待约档'">约档期
    </button>
    <!-- 去改档按钮 - 白底黑边框黑字 -->
    <!-- <button class="cu-btn round line-black margin-right" @tap="goToAppoint"
            :loading="appointLoading" :disabled="appointLoading"
            v-if="orderInfo.isPay === '1' && orderInfo.status==='12' && orderInfo.statusDesc==='待拍摄'">去改档
    </button> -->
    <!-- 申请退款按钮 - 白底黑边框黑字 -->
    <button class="cu-btn round line-black margin-right" @tap="applyRefund"
            v-if="orderInfo.isPay === '1' &&  ((orderInfo.status==='10'||orderInfo.status==='11')  &&  (orderInfo.listOrderItem[0].status !== '1' &&  orderInfo.listOrderItem[0].status !== '2'))">申请退款
    </button>
    <!-- <button class="cu-btn round line-red margin-right" bindtap="urgeOrder" loading="{{loading}}"
  disabled="{{loading}}" type="" wx:if="{{orderInfo.status == '1'}}">
    提醒卖家发货
  </button> -->
    <!--    确认收货-->
    <!--		<button class="cu-btn round line-red margin-right" @tap="orderReceive" :loading="loading" :disabled="loading" type
         v-if="orderInfo.status == '2'">确认收货</button>-->
    <!--    评价-->
    <!--		<button class="cu-btn round line-red margin-right" @tap="orderAppraise" :loading="loading" :disabled="loading" type
         v-if="orderInfo.status == '3' && orderInfo.appraisesStatus == '0'">评价</button>-->
  </view>


</template>

<script>
/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import api from 'utils/api'
import util from 'utils/util'
import jweixin from '@/utils/jweixin'

export default {
  data() {
    return {
      loading: false,
      appointLoading: false // 去约档按钮加载状态
    };
  },

  components: {},
  props: {
    orderInfo: {
      type: Object,
      default: () => ({})
    },
    callPay: {
      type: Boolean,
      default: false
    },
    contact: {
      type: Boolean,
      default: false
    },
    inviteLoading: {
      type: Boolean,
      default: false
    }
  },

  mounted() {
    let that = this;
    console.log(that.orderInfo);
    setTimeout(function () {
      if (that.callPay) {
        that.unifiedOrder();
      }
    }, 1000);

    // 如果是待成团状态，获取分享数据
    if (this.orderInfo && this.orderInfo.isPay === '1' &&
        this.orderInfo.status === '10' && this.orderInfo.statusDesc === '待成团') {
      // this.loadShareData();
    }
  },

  methods: {
    orderReceive() {
      let that = this;
      uni.showModal({
        content: '是否确认收货吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success(res) {
          if (res.confirm) {
            let id = that.orderInfo.id;
            api.orderReceive(id).then(res => {
              that.$emit('orderReceive', res);
            });
          }
        }
      });
    },
    openCustomerService() {
      wx.openCustomerServiceChat({
        extInfo: { url: 'https://work.weixin.qq.com/kfid/kfcfd2e702b9ffd3ba7' }, corpId: 'wwd396b79dd20220ba', success(res) {
          console.log(res);
        }, fail(err) {
          console.log(err);

        },
      })
    },

    orderCancel() {
      let that = this;
      uni.showModal({
        content: '确认取消该订单吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success(res) {
          if (res.confirm) {
            let id = that.orderInfo.id;
            api.orderCancel(id).then(res => {
              that.$emit('orderCancel', res);
            });
          }
        }
      });
    },

    orderDel() {
      let that = this;
      uni.showModal({
        content: '确认删除该订单吗？',
        cancelText: '我再想想',
        confirmColor: '#ff0000',
        success(res) {
          if (res.confirm) {
            let id = that.orderInfo.id;
            api.orderDel(id).then(res => {
              that.$emit('orderDel', res);
            });
          }
        }

      });
    },

    unifiedOrder() {
      this.loading = true;
      var that = this;
      let orderInfo = this.orderInfo;
      api.wxunifiedOrder({
        id: orderInfo.id,
        // #ifdef MP-WEIXIN
        tradeType: 'JSAPI',
        // #endif
        // #ifdef H5
        tradeType: util.isWeiXinBrowser() ? 'JSAPI' : 'MWEB',
        // #endif
        // #ifdef APP-PLUS
        tradeType: 'APP',
        // #endif
      }).then(res => {
        this.loading = false;
        if (orderInfo.paymentPrice <= 0) {
          //0元付款
          that.$emit('unifiedOrder', res);
        } else {
          let payData = res.data;
          // #ifdef MP-WEIXIN
          //微信小程序
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: payData.timeStamp,
            nonceStr: payData.nonceStr,
            package: payData.packageValue,
            signType: payData.signType,
            paySign: payData.paySign,
            success: function (res) {
              that.$emit('unifiedOrder', res);
            },
            fail: function (res) {
              console.log('fail:' + JSON.stringify(res));
            },
            complete: function (res) {
              console.log(res);
            }
          });
          // #endif
          // #ifdef APP-PLUS
          //app支付
          let orderInfo = {
            "appid": payData.appId,
            "noncestr": payData.nonceStr,
            "package": payData.packageValue,
            "partnerid": payData.partnerId,
            "prepayid": payData.prepayId,
            "timestamp": payData.timeStamp,
            "sign": payData.sign
          }
          uni.requestPayment({
            provider: 'wxpay',
            orderInfo: orderInfo,
            success: function (res) {
              that.$emit('unifiedOrder', res);
            },
            fail: function (res) {
              console.log('fail:' + JSON.stringify(res));
            },
            complete: function (res) {
              console.log(res);
            }
          });
          // #endif
          // #ifdef H5
          if (util.isWeiXinBrowser()) {
            //公众号H5
            jweixin.payRequest(payData, res => {
              that.$emit('unifiedOrder', res);
            }, e => {

            })
          }
          // #endif
        }
      }).catch(() => {
        this.loading = false;
      });
    },

    urgeOrder() {
      uni.showToast({
        title: '已提醒卖家发货',
        icon: 'success',
        duration: 2000
      });
    },

    orderLogistics() {
      uni.navigateTo({
        url: '/pages/order/order-logistics/index?id=' + this.orderInfo.orderLogistics.id
      });
    },

    orderAppraise() {
      uni.navigateTo({
        url: '/pages/appraises/form/index?orderId=' + this.orderInfo.id
      });
    },

    /**
     * 处理邀请好友点击事件
     */
    handleInviteFriends() {
      // 触发父组件的邀请好友事件
      this.$emit('inviteFriends', this.orderInfo);
    },

    /**
     * 去约档
     */
    goToAppoint() {
      console.log("去约档，订单信息:", this.orderInfo);

      // 设置加载状态
      this.appointLoading = true;

      // 调用API获取真实的pageId
      const payPageId = this.orderInfo.pageId;
      api.getPageIdByPayPageId(payPageId)
        .then(res => {
          console.log("获取约档页面ID成功:", res);

          // 构建约档页面URL
          const pageId = res.data; // 从API获取的真实pageId
          const componentAppid = 'wx4123b17393e6b77b'; // 写死的component_appid
          const appId = this.orderInfo.appId; // 从订单信息中获取
          const tenantId = this.orderInfo.tenantId; // 从订单信息中获取

          const url = `/pages/appoint/index?page_id=${pageId}&app_id=${appId}&tenant_id=${tenantId}&component_appid=${componentAppid}`;

          console.log('跳转到约档页面:', url);

          // 恢复按钮状态
          this.appointLoading = false;

          // 跳转到约档页面
          uni.navigateTo({
            url: url,
            success: function(res) {
              console.log('跳转成功');
            },
            fail: function(err) {
              console.error('跳转失败:', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none',
                duration: 2000
              });
            }
          });
        })
        .catch(err => {
          console.error("获取约档页面ID失败:", err);

          // 恢复按钮状态
          this.appointLoading = false;

        });
    },

    /**
     * 申请退款
     */
    applyRefund() {
      console.log("申请退款，订单信息:", this.orderInfo);

      // 检查是否有订单项
      if (!this.orderInfo.listOrderItem || this.orderInfo.listOrderItem.length === 0) {
        uni.showToast({
          title: '订单信息异常',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 获取第一个订单项的ID
      const firstOrderItemId = this.orderInfo.listOrderItem[0].id;
      const url = `/pages/order/order-refunds/submit/index?orderItemId=${firstOrderItemId}`;

      console.log('跳转到申请退款页面:', url);

      // 跳转到申请退款页面
      uni.navigateTo({
        url: url,
        success: function() {
          console.log('跳转申请退款页面成功');
        },
        fail: function(err) {
          console.error('跳转申请退款页面失败:', err);
          uni.showToast({
            title: '跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }
  }
};
</script>
<style>
/* 统一按钮样式：白底、黑边框、黑字 */
.cu-btn.line-black {
  background-color: #ffffff !important;
  border: 1px solid #000000 !important;
  color: #000000 !important;
}

.cu-btn.line-black:hover {
  background-color: #f5f5f5 !important;
  border-color: #000000 !important;
  color: #000000 !important;
}

.cu-btn.line-black:active {
  background-color: #e5e5e5 !important;
  border-color: #000000 !important;
  color: #000000 !important;
}

/* 禁用状态 */
.cu-btn.line-black:disabled {
  background-color: #f5f5f5 !important;
  border-color: #cccccc !important;
  color: #999999 !important;
}
</style>
